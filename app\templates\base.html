<!DOCTYPE html>
<html lang="en" data-theme="{{ current_theme }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}PEPE Store - Top #1 Tools for PC{% endblock %}</title>
    <meta name="description" content="Discover powerful tools and premium software for your PC at PEPE Store. Fast downloads, top-rated apps, and more." />
    <meta name="keywords" content="PEPE Store, PC tools, software for PC, premium apps, download software, top PC applications, PC utilities" />
    <meta name="author" content="PEPE Store Team" />
    <meta property="og:title" content="PEPE Store - Top #1 Tools for PC" />
    <meta property="og:description" content="Join the PEPE community and access top-rated tools for Windows. Explore powerful PC software trusted by over 1M users." />
    <meta property="og:image" content="https://pepestore.pythonanywhere.com/static/assets/pepe.jpeg" />
    <meta property="og:url" content="https://pepestore.pythonanywhere.com/" />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="PEPE Store - Top #1 Tools for PC" />
    <meta name="twitter:description" content="Discover and download the best tools for your PC. Fast, reliable, and trusted by thousands." />
    <meta name="twitter:image" content="https://pepestore.pythonanywhere.com/static/assets/pepe.jpeg" />
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-V1E02S28YG"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
    
      gtag('config', 'G-V1E02S28YG');
    </script>

    {% block extra_head %}{% endblock %}
</head>
<body>
    <!-- Glassmorphism Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand pepe-brand" href="{{ url_for('main.index') }}">
                <span class="pepe-logo">🐸</span>
                <span>PEPE Store</span>
                <span class="pepe-tagline">Top #1 Tools for PC</span>
            </a>

            <button class="navbar-toggler glass-button" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.index') }}">Home</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <!-- User Menu - Only show if logged in -->
                    {% if session.user_id %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle"
   href="#"
   role="button"
   data-bs-toggle="dropdown"
   data-bs-display="static"
   data-bs-boundary="viewport"
   aria-expanded="false">
   <i class="bi bi-person-circle" aria-hidden="true"></i> {{ session.username }}
</a>
                        <ul class="dropdown-menu">
                            {% if session.role == 'admin' %}
                            <li><a class="dropdown-item" href="{{ url_for('admin.dashboard') }}">
                                <i class="bi bi-speedometer2"></i> Admin Dashboard
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('admin.apps') }}">
                                <i class="bi bi-list"></i> Manage Apps
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('admin.users') }}">
                                <i class="bi bi-people"></i> Manage Users
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('admin.blocked_ips') }}">
                                <i class="bi bi-shield-x"></i> Blocked IPs
                            </a></li>
                            {% else %}
                            <li><a class="dropdown-item" href="{{ url_for('publisher.dashboard') }}">
                                <i class="bi bi-house"></i> My Dashboard
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('publisher.reports') }}">
                                <i class="bi bi-flag"></i> Reports & Suggestions
                            </a></li>
                            {% endif %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="bi bi-box-arrow-right"></i> Logout
                            </a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.login') }}">
                            <i class="bi bi-box-arrow-in-right"></i> Login
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- Glassmorphism Footer -->
    <footer class="glass-panel py-4 mt-5" style="border-radius: 0; border-left: none; border-right: none; border-bottom: none;">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="pepe-glow">🐸 PEPE Store</h5>
                    <p class="mb-2 opacity-75">Top #1 destination for PC tools and applications</p>
                    <p class="mb-0">
                        💬 Contact us on
                        <a href="https://t.me/pepestoreapps" class="text-decoration-none" style="color: var(--pepe-primary);" target="_blank">
                            Telegram
                        </a>
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0 opacity-75">&copy; 2025 PEPE Store. Powered by the community.</p>
                    <small class="opacity-50">Built with 2025 Glassmorphism Design</small>
                </div>
            </div>
        </div>
    </footer>

    {% block extra_scripts %}{% endblock %}
</body>
</html>
