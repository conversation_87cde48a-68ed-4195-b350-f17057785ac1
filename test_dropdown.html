<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dropdown Test - PEPE Store</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="app/static/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Test Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand pepe-brand" href="#">
                <span class="pepe-logo">🐸</span>
                <span>PEPE Store</span>
                <span class="pepe-tagline">Top #1 Tools for PC</span>
            </a>

            <button class="navbar-toggler glass-button" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#">Home</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <!-- Test User Menu -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" 
                           href="#" 
                           role="button" 
                           data-bs-toggle="dropdown"
                           data-bs-auto-close="true"
                           aria-expanded="false">
                            <i class="bi bi-person-circle me-1"></i>Test User
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#">
                                <i class="bi bi-speedometer2 me-2"></i>Admin Dashboard
                            </a></li>
                            <li><a class="dropdown-item" href="#">
                                <i class="bi bi-list me-2"></i>Manage Apps
                            </a></li>
                            <li><a class="dropdown-item" href="#">
                                <i class="bi bi-people me-2"></i>Manage Users
                            </a></li>
                            <li><a class="dropdown-item" href="#">
                                <i class="bi bi-shield-x me-2"></i>Blocked IPs
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#">
                                <i class="bi bi-box-arrow-right me-2"></i>Logout
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Test Content -->
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1>Dropdown Test Page</h1>
                <p>Click on the "Test User" dropdown in the navbar to test the dropdown functionality.</p>
                
                <!-- Additional test dropdown -->
                <div class="dropdown mt-4">
                    <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        Test Standalone Dropdown
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#">Action</a></li>
                        <li><a class="dropdown-item" href="#">Another action</a></li>
                        <li><a class="dropdown-item" href="#">Something else here</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="app/static/js/app.js"></script>
</body>
</html>
